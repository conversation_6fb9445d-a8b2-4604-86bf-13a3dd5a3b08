import { useCallback, useEffect, useMemo, useState } from 'react'

import { getSettings_UNSAFE } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'

import {
  reportDashboardErrorToSentry,
  useLoadJwtAuthQuery,
  useSisenseAuthenticationMutation,
  useSisenseLoginMutation,
} from './queries'
import { useLoadScriptQuery } from './useLoadScriptQuery'

export default function useSisenseLoginAndAuth() {
  const [isTriggered, setIsTriggered] = useState(false)
  const [status, setStatus] = useState<'success' | 'pending' | 'error'>('pending')

  const { sisenseApi: sisenseURL, sisenseJsUrl } = useTypedSelector(getSettings_UNSAFE)

  // 1. get the jwt token from server
  const sisenseLoginMutation = useSisenseLoginMutation()

  const token = sisenseLoginMutation.data?.token

  // 2. authenticate sisense with the jwt token
  const loadJwtAuthQuery = useLoadJwtAuthQuery({
    url: `${sisenseURL}/jwt?jwt=${token}`,
    enabled: isTriggered && !!token,
  })

  // 3. authenticate the sisense with its auth url /isauth
  const sisenseAuthMutation = useSisenseAuthenticationMutation({ sisenseURL })

  const isAuth = sisenseAuthMutation.data?.isAuthenticated

  // 4. load the sisense.js script file
  const loadSisenseScriptQuery = useLoadScriptQuery({
    name: 'sisense_script',
    url: sisenseJsUrl ?? `${sisenseURL}/js/sisense.js`,
    enabled: isTriggered && !!token && !!isAuth,
  })

  const sisenseLoginMutationMutate = sisenseLoginMutation.mutate
  const trigger = useCallback(() => {
    // NOTE: the reason here only use isTriggered to indicate the loading status is
    // between these queries and mutations, it may have case that one next operation
    // hasn't started yet after the previous one finished.
    setIsTriggered(true)
    setStatus('pending')
    sisenseLoginMutationMutate()
  }, [sisenseLoginMutationMutate])

  const sisenseAuthMutationMutate = sisenseAuthMutation.mutate
  useEffect(() => {
    // step 3
    if (
      sisenseLoginMutation.isSuccess &&
      loadJwtAuthQuery.isSuccess &&
      !loadJwtAuthQuery.isFetching &&
      isTriggered
    ) {
      sisenseAuthMutationMutate()
    }
  }, [
    sisenseAuthMutationMutate,
    isTriggered,
    loadJwtAuthQuery.isSuccess,
    sisenseLoginMutation.isSuccess,
    loadJwtAuthQuery.isFetching,
  ])

  // When all the queries and mutations are done, set the triggered flag as false
  useEffect(() => {
    // final step
    if (
      sisenseLoginMutation.isSuccess &&
      sisenseAuthMutation.isSuccess &&
      loadSisenseScriptQuery.isSuccess &&
      !loadSisenseScriptQuery.isFetching &&
      isTriggered
    ) {
      setIsTriggered(false)
      setStatus('success')
    }
  }, [
    isTriggered,
    loadSisenseScriptQuery.isFetching,
    loadSisenseScriptQuery.isSuccess,
    sisenseAuthMutation.isSuccess,
    sisenseLoginMutation.isSuccess,
  ])

  useEffect(() => {
    if (loadJwtAuthQuery.error) {
      reportDashboardErrorToSentry({
        error: loadJwtAuthQuery.error,
        message: 'Failed to authenticate with jwt!',
      })
    }
  }, [loadJwtAuthQuery.error])

  useEffect(() => {
    if (loadSisenseScriptQuery.error) {
      reportDashboardErrorToSentry({
        error: loadSisenseScriptQuery.error,
        message: 'Failed to get the sisense.js file!',
      })
    }
  }, [loadSisenseScriptQuery.error])

  // if token not exist, set it as error
  useEffect(() => {
    if (loadSisenseScriptQuery.isSuccess && sisenseLoginMutation.isSuccess && !token) {
      setIsTriggered(false)
      setStatus('error')
    }
  }, [loadSisenseScriptQuery.isSuccess, sisenseLoginMutation.isSuccess, token])

  // if not auth
  useEffect(() => {
    if (sisenseAuthMutation.isSuccess && sisenseLoginMutation.isSuccess && !isAuth) {
      setIsTriggered(false)
      setStatus('error')
    }
  }, [isAuth, sisenseAuthMutation.isSuccess, sisenseLoginMutation.isSuccess, token])

  useEffect(() => {
    if (
      sisenseLoginMutation.isError ||
      loadJwtAuthQuery.isError ||
      sisenseAuthMutation.isError ||
      loadSisenseScriptQuery.isError
    )
      setStatus('error')
  }, [
    isTriggered,
    loadJwtAuthQuery.isError,
    loadSisenseScriptQuery.isError,
    sisenseAuthMutation.isError,
    sisenseLoginMutation.isError,
  ])

  return useMemo(() => ({ status, trigger }), [status, trigger])
}
