import * as Sentry from '@sentry/browser'
import { useQuery } from '@tanstack/react-query'

import { createQuery } from 'src/util-functions/react-query-utils'

function fetchScript(url: string) {
  if (document.querySelector(`script[src="${url}"]`) || ENV.CYPRESS_CT_ENV) {
    return Promise.resolve(true) // already exists
  }
  const now = Date.now()

  const scriptElement = document.createElement('script')
  scriptElement.src = url
  scriptElement.type = 'text/javascript'

  document.body.appendChild(scriptElement)

  const result = new Promise((resolve, reject) => {
    scriptElement.onload = () => {
      const loadTime = Date.now() - now
      Sentry.withScope((scope) => {
        scope.setTag('module', 'dashboard-sisense')

        Sentry.captureMessage(`sisense.js loaded in ${loadTime}ms.`)
      })
      resolve(true)
    }
    scriptElement.onerror = reject
  })

  return result
}

export const fetchScriptQueryKeyPrefix = ['dashboard/script_query'] as const

export const fetchScriptQuery = ({
  name,
  url,
  enabled,
}: {
  name: string
  url: string
  enabled: boolean
}) =>
  createQuery({
    queryKey: [...fetchScriptQueryKeyPrefix, name] as const,
    queryFn: () => fetchScript(url),
    enabled,
  })

export const useLoadScriptQuery = (props: {
  name: string
  url: string
  enabled: boolean
}) => useQuery(fetchScriptQuery(props))
