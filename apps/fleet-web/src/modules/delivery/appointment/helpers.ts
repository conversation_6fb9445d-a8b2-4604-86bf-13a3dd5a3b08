import * as R from 'remeda'

import type { FetchAppointmentFormDetailsQuery } from 'src/modules/delivery/api/appointment/useAppointmentFormDetailsQuery'

export const formatDateTimeWithTimezone = (
  date: Date,
  timeSlot: string | undefined,
) => {
  if (!timeSlot) return null

  const timeParts = timeSlot.split(':')
  const hours = timeParts[0].padStart(2, '0')
  const minutes = timeParts.length > 1 ? timeParts[1].padStart(2, '0') : '00'
  let timeZone: string =
    timeSlot.indexOf('+') >= 0
      ? timeSlot.substring(timeSlot.indexOf('+'))
      : timeSlot.substring(timeSlot.indexOf('-'))
  timeZone = timeZone.includes(':') ? timeZone : timeZone + ':00'

  const dateWithTime = new Date(date)
  dateWithTime.setHours(
    timeSlot?.includes('PM') && hours !== '12' ? parseInt(hours) + 12 : parseInt(hours),
    parseInt(minutes),
    0,
    0,
  )

  return `${dateWithTime.getFullYear()}-${String(dateWithTime.getMonth() + 1).padStart(
    2,
    '0',
  )}-${String(dateWithTime.getDate()).padStart(2, '0')}T${String(
    dateWithTime.getHours(),
  ).padStart(2, '0')}:${String(dateWithTime.getMinutes()).padStart(
    2,
    '0',
  )}:00${timeZone}`
}

export const populateDynamicFormField = (
  field: FetchAppointmentFormDetailsQuery.DynamicFormField,
  properties: Record<string, any>,
): FetchAppointmentFormDetailsQuery.DynamicFormField => {
  const fieldCopy = JSON.parse(JSON.stringify(field))

  switch (fieldCopy.type) {
    case 'text':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.TextFieldAttributes
        ).value = properties[fieldCopy.name]
      }
      break

    case 'auto-complete':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
        ).value = properties[fieldCopy.name]
      } else {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
        ).value = ''
      }
      break

    case 'date-picker':
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.DatePickerFieldAttributes
        ).value = properties[fieldCopy.name]
      }
      break

    case 'customer-search':
      if (properties['customer']) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
        ).customer = properties['customer']
      }
      if (properties[fieldCopy.name]) {
        ;(
          fieldCopy.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
        ).address = properties[fieldCopy.name]
      }
      break
    case 'grouped-fields': {
      const propertyValue = properties[fieldCopy.name]

      if (
        !propertyValue ||
        !R.isArray(propertyValue) ||
        !propertyValue.every(R.isPlainObject)
      ) {
        break
      }

      const attributes =
        fieldCopy.attributes as FetchAppointmentFormDetailsQuery.GroupedFieldsAttributes
      const baseGroupedFields = attributes.groupedFields

      if (!baseGroupedFields) {
        break
      }

      const existingGroupedValues = attributes.groupedFieldsValues || []

      const createGroupedFieldList = (
        item: Record<string, any>,
        groupIndex: number,
      ) => {
        const existingGroup = existingGroupedValues[groupIndex]
        const hasExisting =
          existingGroup &&
          R.isArray(existingGroup.groupedFieldList) &&
          existingGroup.groupedFieldList.length > 0

        const baseFields = hasExisting
          ? existingGroup.groupedFieldList
          : structuredClone(baseGroupedFields)

        return baseFields.map((field, fieldIndex) => ({
          ...populateDynamicFormField(field, item),
          key: `${field.name}-${groupIndex}-${fieldIndex}`,
        }))
      }

      const createGroupedValue = (item: Record<string, any>, groupIndex: number) => ({
        key: `group-${groupIndex}`,
        groupedFieldList: createGroupedFieldList(item, groupIndex),
      })

      attributes.groupedFieldsValues = propertyValue.map(createGroupedValue)
      break
    }
  }

  return fieldCopy
}

export const extractKeysAndValuesFromField = (
  field: FetchAppointmentFormDetailsQuery.DynamicFormField,
  jsonObject: Record<string, any>,
) => {
  const obj: Record<string, any> = jsonObject
  switch (field.type) {
    case 'text':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.TextFieldAttributes
      ).value
      break
    case 'auto-complete':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.AutoCompleteFieldAttributes
      ).value
      break
    case 'date-picker':
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.DatePickerFieldAttributes
      ).value
      break
    case 'customer-search':
      obj['customer'] = (
        field.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
      ).customer
      obj[field.name] = (
        field.attributes as FetchAppointmentFormDetailsQuery.CustomerSearchFieldAttributes
      ).address
      break

    case 'grouped-fields': {
      const attributes =
        field.attributes as FetchAppointmentFormDetailsQuery.GroupedFieldsAttributes

      const processedGroups = attributes.groupedFieldsValues.map((groupedFieldList) => {
        const groupData: Record<string, any> = {}

        for (const groupedField of groupedFieldList.groupedFieldList) {
          extractKeysAndValuesFromField(groupedField, groupData)
        }

        return groupData
      })

      obj[field.name] = processedGroups
      break
    }
  }
  return obj
}
