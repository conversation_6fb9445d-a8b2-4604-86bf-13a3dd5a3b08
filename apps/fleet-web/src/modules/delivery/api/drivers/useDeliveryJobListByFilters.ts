// eslint-disable-next-line
/* eslint-disable unicorn/explicit-length-check */
import { isEmpty, sortBy } from 'lodash'
import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import moment from 'moment'
import type { Except } from 'type-fest'

import apiCaller from 'src/api/api-caller'
import {
  fetchCompletedTime,
  parseScheduleDescription,
} from 'src/modules/delivery/api/jobs/useDeliveryJobDetailsQuery'
import type { PlansDetailApiOutput } from 'src/modules/delivery/api/plans/types'
import { parseDeliveryPlanDetails } from 'src/modules/delivery/api/plans/useDeliveryPlanListsQuery'
import {
  JOB_STOP_TYPE_ID,
  type JOB_STATUS_ICON_STYLE,
  type JOB_STOP_STATUS_TO_ID,
  type SCHEDULE_TYPE_ID,
} from 'src/modules/delivery/utils/constants'
//helpers
import {
  formatSpecialEquipments,
  getJobStatusDescription,
  getTimezone,
} from 'src/modules/delivery/utils/helpers'
import type { ValueOf } from 'src/types'
import { ctIntl } from 'src/util-components/ctIntl'

import { generateJobOrderArragementNumber } from '../jobs/helpers'
//types
import type {
  JobDriverApiOutput,
  JobDriverReturn,
  JobItemApiOutput,
  JobItemReturn,
  JobItemTodoApiOutput,
  JobItemTodoReturn,
  JobsDetailReturn,
  JobStopApiOutput,
  JobStopReturn,
  JobStopTodoApiOutput,
  JobStopTodoReturn,
  LookupOptionItems,
} from '../jobs/types'
import type { ScheduleTypeOptionType } from '../lookup/types'
import { getStatusName } from './helpers'

export type LegsData = {
  legId: number
  startStopId: number
  endStopId: number
  projectedPolyline: string
  distance: number
  lastEstimatedTravelTime: number
  driverTravelDistance: number | null
  driverTravelPolyline: string | null
  driverTravelTime: number | null
}

export declare namespace FetchDeliveryJobListsByDriverId {
  type ApiInput = {
    listType?: string
    filters: {
      deliveryDriverId?: string
      jobId?: number
      planId?: number
    }
  }

  type ApiOutput = {
    data: Array<{
      delivery_driver_id: string
      driver: JobDriverApiOutput
      in_progress: boolean
      job_id: number
      plan_id: number | null
      plan: PlansDetailApiOutput | null
      job_item: JobItemApiOutput
      job_status_id: keyof typeof JOB_STATUS_ICON_STYLE
      job_type_id: number
      order_id: string
      create_ts: string
      reference_number: string
      requiredCapabilities?: Array<LookupOptionItems>
      labels?: Array<LookupOptionItems>
      schedule_type: {
        scheduled_type_id: number
        description: string
      }
      schedule_type_id: number
      scheduled_delivery_ts: string | null
      stop: JobStopApiOutput
      subuserId: string | null
      driverLegsInfo: {
        sumActualDistance: number | null
        sumActualTravelTime: number | null
        sumProjectedDistance: number | null
        sumProjectedTravelTime: number | null
      }
      appointment_files:
        | Array<{
            appointment_file_id: number
            file_name: string
            job_id: number
          }>
        | undefined
    }>
    legs: Array<LegsData>
    orderedStopIds: Array<number>
  }

  type Return = {
    data: Array<{
      deliveryDriverId: string
      driver?: JobDriverReturn
      planId: number | null
      plan: ReturnType<typeof parseDeliveryPlanDetails> | null
      originalPlanId: number | null // Added this attribute for comparison for plan assignment as we don't need to pass the planId if is will still use the same
      deliveryDriverIdOrPlanId:
        | string
        | number
        | { value: string | number | null; label: string; type: number }
        | null
      inProgress: boolean
      jobId: number
      jobItem: Array<JobItemReturn>
      jobStatusId: keyof typeof JOB_STATUS_ICON_STYLE
      jobStatusDescription: string
      jobTypeId: number
      orderId: string
      createTs: string
      referenceNumber: string
      requiredCapabilities?: Array<LookupOptionItems>
      jobNumber: string | number
      scheduleType: {
        scheduledTypeId: number
        description: string
      }
      scheduleTypeOptions: ScheduleTypeOptionType | null
      scheduleTypeId: ValueOf<typeof SCHEDULE_TYPE_ID>
      scheduledDeliveryTs: string | null
      stop: Array<JobStopReturn>
      scheduledDescription: {
        isScheduled: boolean
        asap: string
        leftPanelScheduleDescription: string
        leftPanelScheduleDescriptionCountdown: string
        tableScheduleDescription: string
        originalFormattedSchedule: string
        statusDescription: string
      }
      pickupCustomerName: string
      dropOffCustomerName: string
      completedTime: string | null
      trackingNumbers: Array<string>
      trackingNumber: string
      labels?: Array<LookupOptionItems>
      jobs?: Array<JobsDetailReturn> &
        Array<{
          order: number
          multiStopItemId: string
          stop: Array<JobStopReturn>
        }>
      jobListsOrder: number
      subuserId: string | null
      driverLegsInfo: {
        sumActualDistance: number | null
        sumActualTravelTime: number | null
        sumProjectedDistance: number | null
        sumProjectedTravelTime: number | null
      }
      appointmentFiles:
        | Array<{
            appointmentFileId: number
            appointmentFileName: string
            jobId: number
          }>
        | undefined
    }>
    legs: Array<LegsData>
    orderedStopIds: Array<number>
  }
}

const range = 4

const createKey = (data?: FetchDeliveryJobListsByDriverId.ApiInput) => {
  const baseKey = ['delivery/getJobsByDriverId'] as const

  return data === undefined ? baseKey : ([...baseKey, data] as const)
}

const useDeliveryJobListByFilters = (
  data: FetchDeliveryJobListsByDriverId.ApiInput,
  options?: Except<
    UseQueryOptions<FetchDeliveryJobListsByDriverId.Return, Error>,
    'queryKey'
  >,
) => {
  let customOptions = { gcTime: 0, ...options }
  const retryCount = range - 1

  if (customOptions && Object.keys(customOptions).includes('refetchInterval')) {
    customOptions = {
      ...customOptions,
      retry: retryCount,
      retryDelay: (retryAttempt) =>
        Math.min(retryAttempt > 1 ? 2 ** retryAttempt * 10000 : 1000),
    }
  }

  return useQuery<FetchDeliveryJobListsByDriverId.Return, Error>({
    queryKey: createKey(data),
    queryFn: () => fetchDeliveryJobListByFilter(data),
    ...customOptions,
  })
}

export const fetchDeliveryJobListByFilter = (
  data: FetchDeliveryJobListsByDriverId.ApiInput,
): Promise<FetchDeliveryJobListsByDriverId.Return> => {
  /** Forbid empty filters
   * Reverted previous implementation as we need to allow filter hook evethough value from each key is empty (unless filter object is empty) to avoid
   * blocking other use-cases. Suggesting to conditionally remove the filter itself during usage / implementation
   * */

  const { filters } = data
  if (isEmpty(filters)) return Promise.reject()
  return apiCaller('delivery_get_jobs', { data }, { noX: true }).then((res) =>
    parseDeliveryJobLists(res),
  )
}

const parseDeliveryJobLists = (
  jobLists: FetchDeliveryJobListsByDriverId.ApiOutput,
): FetchDeliveryJobListsByDriverId.Return => {
  const formatedJoblists = jobLists.data.map((jobList) => {
    const jobNumber = jobList.reference_number || jobList.order_id
    const trackingNumbers = jobList.job_item
      .map((items) => items.tracking_number)
      .filter((trackingNumber) => trackingNumber != null)

    const pickupCustomer =
      jobList.stop.find((data) => data.stop_type_id === JOB_STOP_TYPE_ID.PICKUP)
        ?.customer_name ||
      jobList.stop.find((data) => data.stop_type_id === JOB_STOP_TYPE_ID.PICKUP)
        ?.customer?.customer_name ||
      '-'
    const dropoffCustomer =
      jobList.stop.find((data) =>
        [JOB_STOP_TYPE_ID.DROPOFF, JOB_STOP_TYPE_ID.SINGLE].includes(data.stop_type_id),
      )?.customer_name ||
      jobList.stop.find((data) =>
        [JOB_STOP_TYPE_ID.DROPOFF, JOB_STOP_TYPE_ID.SINGLE].includes(data.stop_type_id),
      )?.customer?.customer_name ||
      '-'

    return {
      deliveryDriverId: jobList.delivery_driver_id,
      driver: jobList.driver
        ? {
            contactNumber: jobList.driver.contact_number,
            deliveryDriverId: jobList.driver.delivery_driver_id,
            driverStatusId: jobList.driver.driver_status_id,
            email: jobList.driver.email,
            firstName: jobList.driver.first_name,
            isActive: jobList.driver.is_active,
            isOnline: jobList.driver.is_online,
            lastName: jobList.driver.last_name,
            mobileDeviceId: jobList.driver.mobile_device_id,
            phoneCode: jobList.driver.phone_code,
            phoneNumber: jobList.driver.phone_number,
            registration: jobList.driver.registration,
            fullName:
              getStatusName(jobList.job_status_id) ||
              `${jobList.driver?.first_name ?? ''} ${
                jobList.driver?.last_name ?? ''
              }`.trim(),
          }
        : undefined,
      createTs: jobList.create_ts,
      inProgress: jobList.in_progress,
      jobId: jobList.job_id,
      planId: Number(jobList.plan_id),
      plan: jobList.plan ? parseDeliveryPlanDetails(jobList.plan) : null,
      originalPlanId: Number(jobList.plan_id),
      deliveryDriverIdOrPlanId:
        Number(jobList.plan_id) || jobList.delivery_driver_id || null,
      jobItem: parseDeliveryJobItems(jobList.job_item, jobNumber),
      jobStatusId: jobList.job_status_id,
      jobStatusDescription:
        getJobStatusDescription(
          jobList.delivery_driver_id,
          jobList.plan_id,
          jobList.job_status_id,
        ) || '',
      jobTypeId: jobList.job_type_id,
      orderId: jobList.order_id,
      referenceNumber: jobList.reference_number,
      jobNumber,
      requiredCapabilities:
        (jobList.requiredCapabilities &&
          jobList.requiredCapabilities.length &&
          jobList.requiredCapabilities.map((specialEquipment) => ({
            ...specialEquipment,
            name: specialEquipment.userId
              ? specialEquipment.name
              : ctIntl.formatMessage({
                  id: formatSpecialEquipments(specialEquipment.name),
                }),
          }))) ||
        [],
      labels:
        (jobList.labels &&
          jobList.labels.length &&
          jobList.labels.map((label) => ({
            ...label,
            name: label.userId
              ? label.name
              : ctIntl.formatMessage({
                  id: formatSpecialEquipments(label.name),
                }),
          }))) ||
        [],
      scheduleType: {
        scheduledTypeId: jobList.schedule_type_id,
        description: '',
      },
      scheduleTypeOptions: jobList.schedule_type_id
        ? {
            id: jobList.schedule_type_id,
            value: '',
            label: '',
          }
        : null,
      scheduleTypeId: jobList.schedule_type_id,
      scheduledDeliveryTs:
        /*Remove strict check for scheduled_delivery_ts prior to DWA-3139 */
        jobList.scheduled_delivery_ts
          ? moment(jobList.scheduled_delivery_ts)
              .tz(getTimezone())
              .format('YYYY-MM-DD HH:mm')
          : null,
      stop: parseDeliveryStops(
        jobList.stop,
        jobNumber,
        jobList.scheduled_delivery_ts,
        jobList.schedule_type_id,
      ).sort((a, b) => Number(a.ordering) - Number(b.ordering)),
      scheduledDescription: parseScheduleDescription(
        jobList.schedule_type_id,
        jobList.scheduled_delivery_ts,
        jobList.job_status_id,
      ),
      pickupCustomerName: pickupCustomer,
      dropOffCustomerName: dropoffCustomer,
      completedTime: fetchCompletedTime(jobList.stop),
      /* Added trackingNumbers attribute in array format removing the first element to use in table view hover representing a
      job with multiple tracking numbers */
      trackingNumbers: trackingNumbers.slice(1),
      /* Added trackingNumber attribute in string format to use in table view tracking# column to initially
      represent the first updated tracking number in a job*/
      trackingNumber: isEmpty(trackingNumbers) ? '' : trackingNumbers[0],
      jobListsOrder: generateJobOrderArragementNumber(
        jobList.job_status_id,
        jobList.schedule_type_id,
        jobList.delivery_driver_id,
        jobList.plan_id,
        jobList.in_progress,
      ),
      subuserId: jobList.subuserId,
      driverLegsInfo: jobList.driverLegsInfo,
      appointmentFiles: jobList.appointment_files?.map((file) => ({
        appointmentFileId: file.appointment_file_id,
        appointmentFileName: file.file_name,
        jobId: file.job_id,
      })),
    }
  })

  const legs =
    jobLists.legs && jobLists.legs.length > 0
      ? jobLists.legs.map((leg) => ({
          legId: leg.legId,
          startStopId: leg.startStopId,
          endStopId: leg.endStopId,
          projectedPolyline: leg.projectedPolyline,
          distance: leg.distance,
          lastEstimatedTravelTime: leg.lastEstimatedTravelTime,
          driverTravelDistance: leg.driverTravelDistance,
          driverTravelPolyline: leg.driverTravelPolyline,
          driverTravelTime: leg.driverTravelTime,
        }))
      : []

  // sort by create_ts by ASC
  return {
    orderedStopIds: jobLists.orderedStopIds,
    data: sortBy(formatedJoblists, (job) => moment(job.createTs).toDate()),
    legs,
  }
}

const parseDeliveryJobItems = (
  jobItems: JobItemApiOutput,
  jobNumber: string | number,
): Array<JobItemReturn> =>
  jobItems.map((jobItem) => ({
    description: jobItem.description,
    itemTypeId: jobItem.item_type_id,
    jobItemId: jobItem.job_item_id,
    jobItemStatus: jobItem.job_item_status,
    jobItemTodo: parseDeliveryJobItemTodo(jobItem.job_item_todo, jobNumber),
    trackingNumber: jobItem.tracking_number,
    weight: jobItem.weight,
    length: jobItem.length,
    width: jobItem.width,
    height: jobItem.height,
    packageType: jobItem.package_type,
    quantity: jobItem.quantity,
    sku: jobItem.sku,
    upc: jobItem.upc,
  }))

const parseDeliveryJobItemTodo = (
  jobItemTodos: JobItemTodoApiOutput,
  jobNumber: string | number,
): Array<JobItemTodoReturn> =>
  jobItemTodos.map((jobItemTodo) => ({
    jobItemTodoId: jobItemTodo.job_item_todo_id,
    jobItemId: jobItemTodo.job_item_id,
    todoTypeId: jobItemTodo.todo_type_id,
    todoStatusId: jobItemTodo.todo_status_id,
    stopTypeId: jobItemTodo.stop_type_id,
    isRequired: jobItemTodo.is_required,
    tag: jobItemTodo.tag,
    todoStatus: {
      todoStatusId: jobItemTodo.todo_status_id,
      description: '',
    },
    todoType: {
      todoTypeId: jobItemTodo.todo_type_id,
      description: '',
    },
    completeTs: jobItemTodo.complete_ts,
    itemTodoImgs: jobItemTodo.job_item_todo_image
      ? jobItemTodo.job_item_todo_image.map((img) => ({
          customerName: img.customer_name,
          imageId: img.image_id,
          imageUrl: img.image_url,
          itemTodoId: img.job_item_todo_id,
          statusRemarks: img.status_remarks,
          jobNumber,
        }))
      : null,
    statusRemarks: jobItemTodo.status_remarks,
    updateTs: jobItemTodo.update_ts,
    note: jobItemTodo.note,
    description: jobItemTodo.description,
    latitude: jobItemTodo.latitude,
    longitude: jobItemTodo.longitude,
  }))

const parseDeliveryStops = (
  jobStops: JobStopApiOutput,
  jobNumber: string | number,
  scheduledTs: string | null,
  scheduleTypeId: ValueOf<typeof SCHEDULE_TYPE_ID>,
): Array<JobStopReturn> =>
  jobStops.map((jobStop) => {
    const date = new Date()
    const currentDate = moment(date).tz(getTimezone())
    const scheduledDate = moment(scheduledTs).tz(getTimezone())

    return {
      activityStartedTs: jobStop.activity_started_ts,
      activityArrivedTs: jobStop.activity_arrived_ts,
      activityCompletedTs: jobStop.activity_completed_ts,
      activityRejectedTs: jobStop.activity_rejected_ts,
      addressLine1: jobStop.address_line_1,
      addressLine2: jobStop.address_line_2,
      asapPriority: jobStop.asap_priority,
      contactCode: jobStop.contact_code,
      contactNumber: jobStop.contact_number,
      countryId: jobStop.country_id,
      customer: {
        addressLine1: jobStop.address_line_1,
        addressLine2: jobStop.address_line_2,
        contactCode: jobStop.contact_code,
        contactNumber: jobStop.contact_number,
        customerId: jobStop.customer_id,
        customerName: jobStop.customer_name || jobStop?.customer?.customer_name || '',
        email: jobStop.email,
        lat: Number.parseFloat(jobStop.latitude),
        lng: Number.parseFloat(jobStop.longitude),
        postalCode: jobStop.postal_code || null,
      },
      customerId: jobStop.customer_id,
      customerName:
        jobStop.customer_name ||
        (jobStop.customer && jobStop.customer.customer_name) ||
        '',
      email: jobStop.email,
      jobId: jobStop.job_id,
      jobNumber: jobNumber,
      jobArrived: jobStop.activity_arrived_ts,
      jobCompleted: jobStop.activity_completed_ts,
      jobDuration:
        jobStop.activity_duration !== null && jobStop.activity_duration < 1
          ? 1
          : jobStop.activity_duration,
      lat: Number.parseFloat(jobStop.latitude),
      lng: Number.parseFloat(jobStop.longitude),
      note: jobStop.note,
      ordering: jobStop.ordering,
      postalCode: jobStop.postal_code || null,
      scheduledDeliveryTs: scheduledTs,
      statusRemarks: jobStop.status_remarks,
      stopId: jobStop.stop_id,
      stopTypeId: jobStop.stop_type_id,
      stopStatusId: jobStop.stop_status_id as ValueOf<typeof JOB_STOP_STATUS_TO_ID>,
      stopTodo: parseDeliveryStopTodos(jobStop.stop_todo, jobNumber),
      userId: jobStop.user_id,
      scheduleTypeId: scheduleTypeId,
      isScheduledInFuture: scheduledDate.isAfter(currentDate),
      priority: jobStop.priority,
      duration: jobStop.duration,
      ...(jobStop.delivery_windows && {
        deliveryWindows: jobStop.delivery_windows.map((delivery) => ({
          ...(delivery.stop_id && { stopId: delivery.stop_id }),
          timeFrom: delivery.time_from,
          timeTo: delivery.time_to,
        })),
      }),
    }
  })

const parseDeliveryStopTodos = (
  stopTodos: JobStopTodoApiOutput,
  jobNumber: string | number,
): Array<JobStopTodoReturn> =>
  stopTodos.map((stopTodo) => ({
    completeTs: stopTodo.complete_ts,
    stopTodoImgs: stopTodo.stop_todo_image
      ? stopTodo.stop_todo_image.map((todoImg) => ({
          customerName: todoImg.customer_name,
          imageId: todoImg.image_id,
          imageUrl: todoImg.image_url,
          stopTodoId: todoImg.stop_todo_id,
          statusRemarks: todoImg.status_remarks,
          jobNumber,
        }))
      : null,
    stopTodoId: stopTodo.stop_todo_id,
    stopId: stopTodo.stop_id,
    todoStatusId: stopTodo.todo_status_id,
    todoTypeId: stopTodo.todo_type_id,
    isRequired: stopTodo.is_required,
    itemTodoImgs: stopTodo.stop_todo_image.map((img) => ({
      customerName: img.customer_name,
      imageId: img.image_id,
      imageUrl: img.image_url,
      stopTodoId: img.stop_todo_id,
      statusRemarks: img.status_remarks,
    })),
    statusRemarks: stopTodo.status_remarks,
    updateTs: stopTodo.update_ts,
    note: stopTodo.note,
    description: stopTodo.description,
    latitude: stopTodo.latitude,
    longitude: stopTodo.longitude,
  }))

export default Object.assign(useDeliveryJobListByFilters, {
  createKey,
})
