import { isEmpty } from 'lodash'
import moment from 'moment-timezone'

import type { LegsData } from 'src/modules/delivery/api/drivers/useDeliveryJobListByFilters'
//types
import type { JobStopReturn } from 'src/modules/delivery/api/jobs/types'
import { ETA_SIMULATOR_STATUS_ID } from 'src/modules/delivery/utils/constants'
import { STOP_STATUS_ID } from 'src/modules/deliveryRevamp/constants/job'

export type ETAProps = {
  defaultETAISOString?: string
  etaSimulateValue: Date | null
}

type DeliveryLegsData = {
  stops: Array<JobStopReturn>
  totalProjectedDistance: number
  totalProjectedTravelTime: number
  driverLocationETA: Date | null
  driverETASimulationStatus: ETA_SIMULATOR_STATUS_ID
}

export const calcLegsData = (
  legsData: Array<LegsData>,
  accumulatedETT: number,
  accumulatedDuration: number,
  duration: number,
  currentDriverPosition?: LegsData,
  etaProps?: ETAProps,
) => {
  if (!legsData) {
    return 'unserved'
  }

  if (
    legsData &&
    currentDriverPosition &&
    legsData.every((leg) => leg.lastEstimatedTravelTime === 0)
  ) {
    //Check the ETA if drivers is in the first stop
    return moment(etaProps?.etaSimulateValue || etaProps?.defaultETAISOString)
      .add(duration, 'minutes')
      .format('HH:mm')
  }

  const foundValidTimeLeg = legsData.find((leg) => leg?.lastEstimatedTravelTime)

  if (legsData && currentDriverPosition && foundValidTimeLeg) {
    //Check the ETA from drivers location to first stop
    return moment(etaProps?.etaSimulateValue || etaProps?.defaultETAISOString)
      .add(foundValidTimeLeg.lastEstimatedTravelTime, 'seconds')
      .add(duration, 'minutes')
      .format('HH:mm')
  }

  return moment(etaProps?.etaSimulateValue || etaProps?.defaultETAISOString)
    .add(accumulatedETT, 'seconds')
    .add(accumulatedDuration, 'minutes')
    .format('HH:mm') //Check the ETA via summation of succeeding lastEstimatedTravelTime
}

export const getDeliveryLegsData = (
  legs: Array<LegsData>,
  filteredStops: Array<JobStopReturn>,
  orderedStopIds: Array<number>,
  etaProps?: ETAProps,
  withoutLegsInStops?: boolean,
  isPlan?: boolean,
): DeliveryLegsData => {
  let accumulatedETT = 0
  let accumulatedDuration = 0
  let totalProjectedDistance = 0
  let totalProjectedTravelTime = 0
  let driverLocationETA =
    legs && legs.length > 0 && etaProps ? etaProps.etaSimulateValue : null
  let driverETASimulationStatus = ETA_SIMULATOR_STATUS_ID.DRIVER_UNLOCATED

  const stops = filteredStops
    .sort(
      (a, b) =>
        orderedStopIds.indexOf(Number(a.stopId)) -
        orderedStopIds.indexOf(Number(b.stopId)),
    )
    // At the moment Ordering property is not handled properly when we reassign an job to another driver, the ordering seems to be the value of previous driver's stop ordering,
    // To fix the issue, currently we remap this value on FE based on the index value
    .map((stop, index, stops) => {
      const previousStop = stops[index - 1]

      /* Legs data parsing consist of finding a pair legs startStopId and endStopId base on the current order sequence return by
          delivery_driver_stops_optimize endpoint */

      const durationToAdd =
        previousStop &&
        [
          STOP_STATUS_ID.ARRIVED,
          STOP_STATUS_ID.CREATED,
          STOP_STATUS_ID.STARTED,
        ].includes(previousStop.stopStatusId) &&
        previousStop.duration
          ? previousStop.duration
          : 0

      const currentDriverPosition = legs.find(
        (stopLeg) => !stopLeg.startStopId && +stop.stopId === stopLeg.endStopId,
      ) // Check if the current stop is the current driver location

      if (currentDriverPosition && etaProps?.etaSimulateValue) {
        driverLocationETA = moment(etaProps?.etaSimulateValue)
          .add(currentDriverPosition.lastEstimatedTravelTime, 'seconds')
          .toDate()
        driverETASimulationStatus = ETA_SIMULATOR_STATUS_ID.DRIVER_ETA_MODIFIED
      }

      if (currentDriverPosition && !etaProps?.etaSimulateValue) {
        driverLocationETA = moment(etaProps?.defaultETAISOString)
          .add(currentDriverPosition.lastEstimatedTravelTime, 'seconds')
          .toDate()
        driverETASimulationStatus = ETA_SIMULATOR_STATUS_ID.DRIVER_LOCATED
      }

      if (!currentDriverPosition && etaProps?.etaSimulateValue) {
        driverLocationETA = etaProps.etaSimulateValue
      }

      // BE probably return multi legs with same startStopId and endStopId
      const legsData =
        currentDriverPosition && (!stop.jobArrived || !stop.jobCompleted)
          ? [currentDriverPosition]
          : legs.filter(
              (stopLeg) =>
                stopLeg.startStopId === +previousStop?.stopId &&
                +stop.stopId === stopLeg.endStopId,
            )

      if (previousStop && previousStop.duration) {
        accumulatedDuration += previousStop.duration
      }

      //Calculate the summation of all totalProjectedDistance/totalProjectedTravelTime base on the available stops
      if (legsData.length > 0) {
        const foundValidTimeLeg = legsData.find((leg) => leg?.lastEstimatedTravelTime)
        const foundValidDistanceLeg = legsData.find((leg) => leg?.distance)

        if (!stop.jobArrived || !stop.jobCompleted) {
          accumulatedETT += foundValidTimeLeg?.lastEstimatedTravelTime || 0
        }

        totalProjectedDistance += foundValidDistanceLeg?.distance || 0
        totalProjectedTravelTime += foundValidTimeLeg?.lastEstimatedTravelTime || 0
      }

      if (filteredStops.length - 1 === index) {
        // Count the distance and time from last stop to end location also
        const lastStopToEndLocationLeg = legs.find(
          (stopLeg) => !stopLeg.endStopId && +stop.stopId === stopLeg.startStopId,
        )
        if (lastStopToEndLocationLeg) {
          totalProjectedDistance += lastStopToEndLocationLeg.distance || 0
          totalProjectedTravelTime +=
            lastStopToEndLocationLeg.lastEstimatedTravelTime || 0
        }
      }

      const isForFuture = isPlan ? false : stop.isScheduledInFuture

      return {
        ...stop,
        ordering: index + 1,
        ...(!withoutLegsInStops && {
          legs: legsData,
        }),
        legsETA:
          !legs ||
          isEmpty(legs) ||
          stop.jobArrived ||
          stop.jobCompleted ||
          !etaProps ||
          isForFuture
            ? null
            : calcLegsData(
                legsData,
                accumulatedETT,
                accumulatedDuration,
                durationToAdd,
                currentDriverPosition,
                etaProps,
              ),
      }
    })

  return {
    stops,
    totalProjectedDistance,
    totalProjectedTravelTime,
    driverLocationETA,
    driverETASimulationStatus,
  }
}
