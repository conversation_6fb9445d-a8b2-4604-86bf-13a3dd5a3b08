/* eslint-disable no-nested-ternary */
import { useMemo } from 'react'
import { isEmpty, uniqBy } from 'lodash'
import type { UseQueryResult } from '@tanstack/react-query'
import moment from 'moment-timezone'
import * as R from 'remeda'

import useDeliveryJobListByFilters, {
  type FetchDeliveryJobListsByDriverId,
} from 'src/modules/delivery/api/drivers/useDeliveryJobListByFilters'
import { StopStatus, type JobStopReturn } from 'src/modules/delivery/api/jobs/types'
//types
import type { ScheduleTypeOptionType } from 'src/modules/delivery/api/lookup/types'
//api
import useGetLookQuery from 'src/modules/delivery/api/lookup/useGetLookupQuery'
import {
  getDeliveryLegsData,
  type ETAProps,
} from 'src/modules/delivery/right-panel/helpers'
import {
  DELIVERY_SYNC_DATA_INTERVAL,
  ETA_SIMULATOR_STATUS_ID,
  JOB_STOP_STATUS_TO_ID,
  JOB_STOP_TYPE_ID,
  SCHEDULE_TYPE_ID,
} from 'src/modules/delivery/utils/constants'
//helpers
import { ctIntl } from 'src/util-components/ctIntl'

export type DeliveryDriverStopsData = {
  stops: Array<JobStopReturn & { legsETA?: string | null }>
  futureStops: Array<JobStopReturn & { legsETA?: string | null }>
  currentStops: Array<JobStopReturn & { legsETA?: string | null }>
  parsedJobs: FetchDeliveryJobListsByDriverId.Return['data']
  deduplicatedJobs: FetchDeliveryJobListsByDriverId.Return['data']
  legs: FetchDeliveryJobListsByDriverId.Return['legs']
  progressData?: {
    completedStops: number
    totalStops: number
    jobProgressPercentage: number
    jobProgressLabel: string
  }
  query: UseQueryResult<FetchDeliveryJobListsByDriverId.Return, Error>
  totalProjectedDistance: number
  totalProjectedTravelTime: number
  driverLocationETA: Date | null
  driverETASimulationStatus: ETA_SIMULATOR_STATUS_ID
  hasStartedJobs: boolean
}

const useDeliveryDriverStopsData = (
  deliveryDriverId: string | undefined,
  etaProps?: ETAProps,
  withoutLegsInStops?: boolean,
): DeliveryDriverStopsData => {
  const deliveryDriverJobs = useDeliveryJobListByFilters(
    {
      listType: 'jobsUntilToday',
      filters: { deliveryDriverId },
    },
    {
      enabled: !!deliveryDriverId,
      refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
    },
  )
  const lookup = useGetLookQuery()
  let scheduleTypeOptions: Array<ScheduleTypeOptionType> = []
  if (lookup.status === 'success') {
    scheduleTypeOptions = lookup.data.scheduleTypeOptions
  }

  return useMemo(() => {
    if (
      deliveryDriverJobs.status === 'success' &&
      (deliveryDriverId ||
        (etaProps && (etaProps.defaultETAISOString || etaProps.etaSimulateValue)))
    ) {
      const sortedJobs = deliveryDriverJobs.data.data.sort((prev, next) => {
        if (prev?.jobId && next?.jobId) {
          return prev?.jobId < next?.jobId ? -1 : 1
        }
        return 0
      })

      const filteredStops = sortedJobs.flatMap((n) => n.stop)

      //Adding legs
      const legs = deliveryDriverJobs.data.legs

      //Mocking current driver location
      const currentDriverPosition = legs.find((stopLeg) => stopLeg.startStopId === 0)

      const latestCurrentLocation = filteredStops
        .sort((a, b) => Number(a.ordering) - Number(b.ordering))
        .find((stops) => !stops.jobArrived || !stops.jobCompleted)

      if (!currentDriverPosition) {
        //Mock first pick up ETA in the FE side if missing
        const mockFirstPickupETA =
          !isEmpty(filteredStops) && R.isNonNullish(latestCurrentLocation)
            ? legs.find((leg) => leg.startStopId === +latestCurrentLocation.stopId)
            : null

        if (
          legs &&
          !isEmpty(legs) &&
          mockFirstPickupETA &&
          R.isNonNullish(latestCurrentLocation)
        ) {
          legs.push({
            startStopId: 0,
            endStopId: +latestCurrentLocation.stopId,
            lastEstimatedTravelTime: 0,
            distance: 0,
            projectedPolyline: '',
            legId: 0,
            driverTravelDistance: null,
            driverTravelPolyline: null,
            driverTravelTime: null,
          })
        }
      }

      const {
        stops,
        totalProjectedDistance,
        totalProjectedTravelTime,
        driverLocationETA,
        driverETASimulationStatus,
      } = getDeliveryLegsData(
        legs,
        filteredStops,
        deliveryDriverJobs.data.orderedStopIds,
        etaProps,
        withoutLegsInStops,
      )

      const jobs = deliveryDriverJobs.data.data

      const futureStops: Array<JobStopReturn> = []
      const currentStops: Array<JobStopReturn> = []

      for (const stop of stops) {
        if (
          stop.isScheduledInFuture &&
          stop.scheduleTypeId === SCHEDULE_TYPE_ID.SCHEDULE
        ) {
          futureStops.push(stop)
        } else {
          currentStops.push(stop)
        }
      }
      if (currentStops.length > 0) {
        currentStops.sort((a, b) => {
          if (a.jobId === b.jobId) {
            return a.stopTypeId === JOB_STOP_TYPE_ID.PICKUP ? -1 : 1
          }

          if (a.jobCompleted && b.jobCompleted) {
            return moment(a.jobCompleted).diff(moment(b.jobCompleted))
          }

          if (a.jobCompleted) return -1
          if (b.jobCompleted) return 1

          return a.ordering - b.ordering
        })
      }

      if (futureStops.length > 0) {
        futureStops.sort((a, b) => {
          if (!moment(a.scheduledDeliveryTs).isSame(moment(b.scheduledDeliveryTs))) {
            return moment(a.scheduledDeliveryTs).diff(moment(b.scheduledDeliveryTs))
          }

          return Number(a.stopId) - Number(b.stopId)
        })
      }

      const parsedJobs = jobs.map((job) => ({
        ...job,
        scheduleType: {
          ...job.scheduleType,
          description:
            scheduleTypeOptions.find(
              (option) => Number(option.value) === job.scheduleTypeId,
            )?.label || '',
        },
      }))

      const hasStartedJobs = stops.some(
        (job) => job.stopStatusId === JOB_STOP_STATUS_TO_ID.STARTED,
      )

      const deduplicatedJobs = uniqBy(parsedJobs, (job) => job.jobId)

      const totalStops = currentStops.length
      const completedStops = filteredStops.reduce(
        (prevItem, nextItem) =>
          nextItem.stopStatusId === StopStatus.COMPLETED ||
          nextItem.stopStatusId === StopStatus.REJECTED
            ? prevItem + 1
            : prevItem,
        0,
      )

      const jobProgressPercentage = Math.round(
        ((completedStops && totalStops && completedStops / totalStops) || 0) * 100,
      )

      let jobProgressLabel = ''

      if (totalStops !== 0) {
        if (jobProgressPercentage === 0) {
          jobProgressLabel = `${ctIntl.formatMessage(
            {
              id: '{percentage}% done',
            },
            {
              values: {
                percentage: 0,
              },
            },
          )}`
        }

        if (jobProgressPercentage > 0 && jobProgressPercentage < 100) {
          jobProgressLabel = `${ctIntl.formatMessage(
            {
              id: '{percentage}% done',
            },
            {
              values: {
                percentage: jobProgressPercentage,
              },
            },
          )}`
        }

        if (jobProgressPercentage === 100) {
          jobProgressLabel = `${ctIntl.formatMessage(
            {
              id: '{percentage}% done',
            },
            {
              values: {
                percentage: 100,
              },
            },
          )}`
        }
      }

      return {
        stops,
        futureStops,
        currentStops,
        parsedJobs,
        deduplicatedJobs,
        progressData: {
          completedStops,
          totalStops,
          jobProgressPercentage,
          jobProgressLabel,
        },
        legs: deliveryDriverJobs.data.legs,
        query: deliveryDriverJobs,
        totalProjectedDistance,
        totalProjectedTravelTime,
        driverLocationETA,
        driverETASimulationStatus,
        hasStartedJobs,
      }
    }

    return {
      stops: [],
      futureStops: [],
      currentStops: [],
      parsedJobs: [],
      deduplicatedJobs: [],
      legs: [],
      progressData: undefined,
      query: deliveryDriverJobs,
      totalProjectedDistance: 0,
      totalProjectedTravelTime: 0,
      driverLocationETA: null,
      driverETASimulationStatus: ETA_SIMULATOR_STATUS_ID.DRIVER_UNLOCATED,
      hasStartedJobs: false,
    }
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deliveryDriverId, deliveryDriverJobs.data, deliveryDriverJobs.status, etaProps])
}
export default useDeliveryDriverStopsData
