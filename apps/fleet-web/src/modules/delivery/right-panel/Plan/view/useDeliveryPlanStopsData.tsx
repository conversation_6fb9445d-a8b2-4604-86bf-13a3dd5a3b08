/* eslint-disable react-hooks/react-compiler */
import { useMemo } from 'react'
import { isEmpty, uniqBy } from 'lodash'
import moment from 'moment-timezone'

import useDeliveryJobListByFilters, {
  type FetchDeliveryJobListsByDriverId,
} from 'src/modules/delivery/api/drivers/useDeliveryJobListByFilters'
import type { JobStopReturn } from 'src/modules/delivery/api/jobs/types'
//types
import type { ScheduleTypeOptionType } from 'src/modules/delivery/api/lookup/types'
//api
import useGetLookQuery from 'src/modules/delivery/api/lookup/useGetLookupQuery'
//helpers
import {
  getDeliveryLegsData,
  type ETAProps,
} from 'src/modules/delivery/right-panel/helpers'
import { ETA_SIMULATOR_STATUS_ID } from 'src/modules/delivery/utils/constants'

type DeliveryPlanStopsData = {
  stops: Array<JobStopReturn>
  futureStops: Array<JobStopReturn & { legsETA?: string | null }>
  currentStops: Array<JobStopReturn & { legsETA?: string | null }>
  parsedJobs: FetchDeliveryJobListsByDriverId.Return['data']
  deduplicatedJobs: FetchDeliveryJobListsByDriverId.Return['data']
  legs: FetchDeliveryJobListsByDriverId.Return['legs']
  isPlanStopsLoading: boolean
  totalProjectedDistance: number
  totalProjectedTravelTime: number
  driverLocationETA: Date | null
  driverETASimulationStatus: ETA_SIMULATOR_STATUS_ID
}

const useDeliveryPlanStopsData = (
  planId: number | null,
  orderedStopIds: Array<number>,
  etaProps?: ETAProps,
): DeliveryPlanStopsData => {
  const deliveryDriverJobs = useDeliveryJobListByFilters(
    {
      filters: {
        planId: planId || undefined,
      },
    },
    { enabled: !!planId },
  )
  const lookup = useGetLookQuery()
  const orderedStopIdsIndex = orderedStopIds.map((stopId, index: number) => ({
    stopId,
    index: index + 1,
  }))
  let scheduleTypeOptions: Array<ScheduleTypeOptionType> = []
  if (lookup.status === 'success') {
    scheduleTypeOptions = lookup.data.scheduleTypeOptions
  }

  const value = useMemo(() => {
    if (
      deliveryDriverJobs.status === 'success' &&
      (planId !== null ||
        (etaProps && (etaProps.defaultETAISOString || etaProps.etaSimulateValue)))
    ) {
      const filteredStops = deliveryDriverJobs.data.data.flatMap((n) => n.stop)

      const legs = deliveryDriverJobs.data.legs

      const reorderedStops = filteredStops
        .map((stop) => ({
          ...stop,
          stopId: stop.stopId,
          orderedStopIds: orderedStopIdsIndex.find(
            (n) => n.stopId === Number(stop.stopId),
          )?.index,
        }))
        .sort((a, b) => Number(a.orderedStopIds) - Number(b.orderedStopIds))
        .map((stop, index) => ({ ...stop, ordering: index + 1 }))

      //Mock first pick up ETA in the FE side exclusive for plans only
      const mockFirstPickupETA =
        !isEmpty(reorderedStops) && !isEmpty(reorderedStops[0])
          ? legs.find((leg) => leg.endStopId === +reorderedStops[0].stopId)
          : null

      if (
        legs &&
        !isEmpty(legs) &&
        !mockFirstPickupETA &&
        !isEmpty(reorderedStops[0])
      ) {
        legs.push({
          startStopId: 0,
          endStopId: isEmpty(reorderedStops[0]) ? 0 : +reorderedStops[0].stopId,
          lastEstimatedTravelTime: 0,
          distance: 0,
          projectedPolyline: '',
          legId: 0,
          driverTravelDistance: null,
          driverTravelPolyline: null,
          driverTravelTime: null,
        })
      }

      const {
        stops,
        totalProjectedDistance,
        totalProjectedTravelTime,
        driverLocationETA,
        driverETASimulationStatus,
      } = getDeliveryLegsData(
        legs,
        reorderedStops,
        deliveryDriverJobs.data.orderedStopIds,
        etaProps,
        false,
        true,
      )

      const jobs = deliveryDriverJobs.data.data

      const futureStops: Array<JobStopReturn> = []
      const currentStops: Array<JobStopReturn> = stops
      // Show all the stops in plan view:
      // https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/2148
      // for (const stop of stops) {
      //   if (stop.isScheduledInFuture) {
      //     futureStops.push(stop)
      //   } else {
      //     currentStops.push(stop)
      //   }
      // }

      if (futureStops.length > 0) {
        futureStops.sort((a, b) =>
          moment(a.scheduledDeliveryTs).diff(moment(b.scheduledDeliveryTs)),
        )
      }

      const parsedJobs = jobs.map((job) => ({
        ...job,
        scheduleType: {
          ...job.scheduleType,
          description:
            scheduleTypeOptions.find(
              (option) => Number(option.value) === job.scheduleTypeId,
            )?.label || '',
        },
      }))

      const deduplicatedJobs = uniqBy(parsedJobs, (job) => job.jobId)

      return {
        stops,
        futureStops,
        currentStops,
        parsedJobs,
        deduplicatedJobs,
        legs: deliveryDriverJobs.data.legs,
        isPlanStopsLoading: deliveryDriverJobs.isPending,
        totalProjectedDistance,
        totalProjectedTravelTime,
        driverLocationETA,
        driverETASimulationStatus,
      }
    }

    return {
      stops: [],
      futureStops: [],
      currentStops: [],
      parsedJobs: [],
      deduplicatedJobs: [],
      legs: [],
      isPlanStopsLoading: deliveryDriverJobs.isPending,
      totalProjectedDistance: 0,
      totalProjectedTravelTime: 0,
      driverLocationETA: null,
      driverETASimulationStatus: ETA_SIMULATOR_STATUS_ID.DRIVER_UNLOCATED,
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    planId,
    orderedStopIds,
    deliveryDriverJobs.data,
    deliveryDriverJobs.status,
    deliveryDriverJobs.isPending,
    etaProps?.defaultETAISOString,
    etaProps?.etaSimulateValue,
  ])

  return value
}
export default useDeliveryPlanStopsData
