import { useMutation, useQueryClient } from '@tanstack/react-query'

import { apiCallerNoX } from 'api/api-caller'
import { makeMutationErrorHandlerWithSnackbar } from 'api/helpers'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  parseScorecardConfiguration,
  parseScorecardWeightage,
  scorecardConfigurationWeightageQuery,
} from './queries'
import type { ScorecardConfigurationsMutate, ScorecardWeightageMutate } from './types' // Assuming ScorecardWeightageMutate will be defined in types.ts

export const useUpdateScorecardConfigurationsMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (apiInput: ScorecardConfigurationsMutate.ApiInput) =>
      apiCallerNoX<ScorecardConfigurationsMutate.ApiOutput>(
        'ct_fleet_scorecard_set_user_configuration',
        apiInput,
      ),
    onSuccess: (res) => {
      const oldData = scorecardConfigurationWeightageQuery().getData(queryClient)
      if (!oldData) {
        // Don't have old data, invalidate the query to refetch it and put mutation pending
        return queryClient.invalidateQueries(scorecardConfigurationWeightageQuery())
      }

      const parsedRes = parseScorecardConfiguration(res)
      scorecardConfigurationWeightageQuery().setData(queryClient, {
        updater: {
          ...oldData,
          configurations: parsedRes,
        },
      })
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Scorecard configurations updated successfully' }),
        { variant: 'success' },
      )
      return
    },
    ...makeMutationErrorHandlerWithSnackbar(),
  })
}

export const useUpdateScorecardWeightageMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (apiInput: ScorecardWeightageMutate.ApiInput) =>
      apiCallerNoX<ScorecardWeightageMutate.ApiOutput>(
        'ct_fleet_scorecard_set_user_weightages',
        apiInput,
      ),
    onSuccess: (data) => {
      const oldData = scorecardConfigurationWeightageQuery().getData(queryClient)
      if (!oldData) {
        // Don't have old data, invalidate the query to refetch it and put mutation pending
        return queryClient.invalidateQueries(scorecardConfigurationWeightageQuery())
      }

      const parsedData = parseScorecardWeightage(data)

      scorecardConfigurationWeightageQuery().setData(queryClient, {
        updater: {
          ...oldData,
          weightageData: parsedData,
        },
      })
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({ id: 'Scorecard weightage updated successfully' }),
        { variant: 'success' },
      )
      return
    },
    ...makeMutationErrorHandlerWithSnackbar(),
  })
}
